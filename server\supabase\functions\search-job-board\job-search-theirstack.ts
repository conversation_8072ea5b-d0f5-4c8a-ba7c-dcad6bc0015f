import { CustomerSearchCriteria, Job, PayFrequency, JobType, JobStatus } from '../shared/common.ts';
import { JobBoardSearch } from './index.ts';
/*
 * stuff related to searching theirstack.com job board.
 */



export class TheirStackJobBoard implements JobBoardSearch {

  readonly theirStack: TheirStack;

  constructor(theirStack: TheirStack | undefined) {
    if (!theirStack) {
      console.log("Using mock TheirStack for testing");
      this.theirStack = new MockTheirStack();
    } else {
      console.log("Using live TheirStack for production");
      this.theirStack = new LiveTheirStack();
    }
  }

  async search(searchCriteria: CustomerSearchCriteria): Promise<Job[]> {

    console.log("searching theirstack.com job board with criteria:", searchCriteria);

    // translate the search criteria into theirstack search
    const search = toTheirStackSearch(searchCriteria);

    // perform job search
    const response = await this.theirStack.search(search);
    console.log("theirstack.com job search response:", response);

    // TODO: translate response into Job[]

    if (response && response.data && response.data.length > 0) {
      const jobs: Job[] = toInternalJobs(response);
      console.log("theirstack.com job search found jobs:", jobs);
      return jobs;
    }

    return [];
  }

}


/**
 * Interface to the API. This allows for mocking during tests to avoid tests costing $$
 */
export interface TheirStack {
  /**
   * 
   * @param searchCriteria 
   * @return the raw JSON response from theirstack.com job search
   */
  search(jobSearchRequest: TheirStackSearch): Promise<TheirStackResponse>;
}

/**
 * TheirStack.com job search JSON request:
 * https://api.theirstack.com/#tag/jobs/post/v1/jobs/search
 */
export interface TheirStackSearch {
  page: number,
  limit: number,
  blur_company_data: boolean,
  /**
   * primary mechanism to avoid pulling duplicate jobs. This will 
   * only allow jobs posted previous day to be returned. The first
   * time a we search for a customer, this can be higher to pull 
   * more historical jobs, but should remain 1 thereafter.
   */
  posted_at_max_age_days: number,
  /**
   * job title, comma separated
   */
  job_title_pattern_or: string[],

  job_location_pattern_or: string[]
}

interface TheirStackJob {
  id: number;
  job_title: string;
  url: string;
  date_posted: string;
  location: string;
  remote: boolean;
  company: string;
  salary_string: string;
  salary_currency: string;
  description: string;
  easy_apply: boolean;
  company_object: {
    name: string;
    domain: string;
    logo?: string;
    industry?: string;
  };
}

export interface TheirStackResponse {
  // build out the response object from the test data below

  metadata: {
    total_results: number;
    truncated_results: number;
    truncated_companies: number;
    total_companies: number;
  };
  data: TheirStackJob[];
}



// normalize theirstack salary string to jobs pay_amount field number
function extractHighEndSalary(salaryStr: string): number {
  if (!salaryStr) return 0;

  const salaryMatch = salaryStr.match(/(\d+(?:\.\d+)?)([kKmMbB]?)/g);
  if (!salaryMatch || salaryMatch.length === 0) return 0;

  const last = salaryMatch[salaryMatch.length - 1];
  const match = last.match(/(\d+(?:\.\d+)?)([kKmMbB]?)/);
  if (!match) return 0;

  const value = parseFloat(match[1]);
  const suffix = match[2]?.toLowerCase();

  switch (suffix) {
    case "k": return Math.round(value * 1_000);
    case "m": return Math.round(value * 1_000_000);
    case "b": return Math.round(value * 1_000_000_000);
    default: return Math.round(value); 
  }
}

/**
 * Parses TheirStack job response into internal Job[]
 */
function toInternalJobs(response: TheirStackResponse): Job[] {
  return response.data.map((job): Job => ({
    id: `theirstack-${job.id}`,
    title: job.job_title,
    url: job.url,
    location: job.location,
    description: job.description,
    employer: job.company || job.company_object?.name || "",
    pay_amount: extractHighEndSalary(job.salary_string) || 0,
    pay_frequency: PayFrequency.Monthly,
    pay_currency: job.salary_currency,
    languages: "",
    visa_required: false,
    job_type: JobType.FULLTIME,
    job_status: JobStatus.OPEN,
    account_required: !job.easy_apply,
    customer_apply: false,
  }));
}

export class LiveTheirStack implements TheirStack {

  async search(jobSearchRequest: TheirStackSearch): Promise<TheirStackResponse> {
    console.log("using key", Deno.env.get("THEIRSTACK_API_KEY"));
    const searchStr: string = JSON.stringify(jobSearchRequest);
    console.log("TheirStack search requst:\n", searchStr);
    const response = await fetch('https://api.theirstack.com/v1/jobs/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        Authorization: `Bearer ${Deno.env.get("THEIRSTACK_API_KEY")}`
      },
      body: searchStr
    });

    if (!response.ok) {
      throw new Error(`TheirStack API request failed with status: ${response.status}`);
    }

    const responseStr = await response.text();
    console.log("TheirStack search response:\n", responseStr);
    const json = await response.json();
    return json as TheirStackResponse;
  }
}


// Mock implementation of TheirStack for testing purposes
export class MockTheirStack implements TheirStack {
  async search(_: TheirStackSearch): Promise<TheirStackResponse> {
    return {
      metadata: {
        total_results: 1,
        truncated_results: 0,
        total_companies: 1,
        truncated_companies: 0
      },
      data: [
        {
          id: 1234,
          job_title: "Software Engineer",
          url: "https://example.com/job/1234",
          location: "Remote",
          date_posted: "2024-01-01",
          company: "MockCompany",
          remote: true,
          salary_string: "$100k-$120k",
          salary_currency: "dollars",
          description: "This is a mock job for testing.",
          easy_apply: true,
          company_object: {
            name: "Google",
            domain: "google.com",
            industry: "internet",
            logo: "https://example.com/logo.png",
          }
        }
      ],
    };
  }
}


/**
 * Converts CustomerSearchCriteria into a TheirStackSearch request
 */
function toTheirStackSearch(searchCriteria: CustomerSearchCriteria): TheirStackSearch {
  return {
    page: 0,
    limit: 10,
    blur_company_data: true, // set true for testing
    posted_at_max_age_days: 1,
    job_title_pattern_or: searchCriteria.job_titles,
    job_location_pattern_or: searchCriteria.locations
  }
}

// an example response from api.theirstack.com
const mockResponse = `
{
  "metadata": {
    "total_results": 2034,
    "truncated_results": 0,
    "truncated_companies": 0,
    "total_companies": 1045
  },
  "data": [
    {
      "id": 1234,
      "job_title": "Software Engineer",
      "url": "https://example.com/job/1234",
      "date_posted": "2021-01-01",
      "has_blurred_data": false,
      "company": "string",
      "final_url": "string",
      "source_url": "string",
      "location": "string",
      "short_location": "string",
      "long_location": "string",
      "state_code": "string",
      "latitude": 1,
      "longitude": 1,
      "postal_code": "string",
      "remote": true,
      "hybrid": true,
      "salary_string": "string",
      "min_annual_salary": 1,
      "min_annual_salary_usd": 1,
      "max_annual_salary": 1,
      "max_annual_salary_usd": 1,
      "avg_annual_salary_usd": 1,
      "salary_currency": "string",
      "countries": [
        "string"
      ],
      "country": "United States",
      "cities": [
        "New York City"
      ],
      "continents": [
        "string"
      ],
      "seniority": "c_level",
      "country_codes": [
        "US",
        "CA",
        "ES",
        "FR",
        "AU"
      ],
      "country_code": "US",
      "discovered_at": "2024-01-01T00:00:00",
      "company_domain": "acme.com",
      "hiring_team": [
        {
          "first_name": "John Doe",
          "full_name": "John Doe",
          "role": "CEO",
          "image_url": "https://media.licdn.com/dms1234567890",
          "thumbnail_url": "https://media.licdn.com/dms1234567890",
          "linkedin_url": "https://www.linkedin.com/in/john-doe-1234567890"
        }
      ],
      "reposted": true,
      "date_reposted": "2024-01-01",
      "employment_statuses": [
        "string"
      ],
      "easy_apply": true,
      "description": "string",
      "company_object": {
        "name": "Google",
        "domain": "google.com",
        "industry": "internet",
        "country": "United States",
        "country_code": "string",
        "employee_count": 7543,
        "logo": "https://example.com/logo.png",
        "num_jobs": 746,
        "num_technologies": 746,
        "possible_domains": [],
        "url": "google.com",
        "industry_id": 1,
        "linkedin_url": "http://www.linkedin.com/company/google",
        "num_jobs_last_30_days": 34,
        "num_jobs_found": 23,
        "yc_batch": "W21",
        "apollo_id": "5b839bd0324d4445051f9a5a",
        "linkedin_id": "string",
        "url_source": "string",
        "is_recruiting_agency": true,
        "id": "string",
        "founded_year": 2019,
        "annual_revenue_usd": *********,
        "annual_revenue_usd_readable": "189M",
        "total_funding_usd": 500000,
        "last_funding_round_date": "2020-01-01",
        "last_funding_round_amount_readable": "$1.2M",
        "employee_count_range": "1001-5000",
        "long_description": "Google is a California-based multinational technology company that offers internet-related services such as a search engine, online advertising and cloud computing.",
        "seo_description": "string",
        "city": "Mountain View",
        "postal_code": "28040",
        "company_keywords": [
          "string"
        ],
        "alexa_ranking": 1,
        "publicly_traded_symbol": "GOOG",
        "publicly_traded_exchange": "NASDAQ",
        "investors": [
          "string"
        ],
        "funding_stage": "angel",
        "has_blurred_data": false,
        "technology_slugs": [
          "kafka",
          "elasticsearch"
        ]
      },
      "normalized_title": "string",
      "manager_roles": [
        "string"
      ],
      "matching_phrases": [],
      "matching_words": []
    }
  ]
}
`