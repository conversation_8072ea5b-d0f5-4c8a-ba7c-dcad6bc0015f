
create type "public"."search_status" as enum ('NEW', 'COMPLETE');

create table "public"."customer_search_criteria" (
    "created_at" timestamp with time zone not null default now(),
    "customer_id" uuid not null default gen_random_uuid(),
    "job_titles" character varying[] not null,
    "locations" character varying[] not null,
    "search_date" date not null,
    "search_status" search_status not null default 'NEW'::search_status,
    "id" uuid not null default gen_random_uuid(),
    "job_board_key" text not null default 'THEIR_STACK'::text
);


alter table "public"."customer_search_criteria" enable row level security;

CREATE UNIQUE INDEX customer_search_criteria_pkey ON public.customer_search_criteria USING btree (id);

CREATE UNIQUE INDEX job_boards_key_key ON public.job_boards USING btree (key);

alter table "public"."customer_search_criteria" add constraint "customer_search_criteria_pkey" PRIMARY KEY using index "customer_search_criteria_pkey";

alter table "public"."customer_search_criteria" add constraint "customer_search_criteria_customer_id_fkey" FOREIGN KEY (customer_id) REFERENCES customers(id) not valid;

alter table "public"."customer_search_criteria" validate constraint "customer_search_criteria_customer_id_fkey";

alter table "public"."customer_search_criteria" add constraint "customer_search_criteria_job_board_key_fkey" FOREIGN KEY (job_board_key) REFERENCES job_boards(key) not valid;

alter table "public"."customer_search_criteria" validate constraint "customer_search_criteria_job_board_key_fkey";

alter table "public"."job_boards" add constraint "job_boards_key_key" UNIQUE using index "job_boards_key_key";

grant delete on table "public"."customer_search_criteria" to "authenticated";

grant insert on table "public"."customer_search_criteria" to "authenticated";

grant references on table "public"."customer_search_criteria" to "authenticated";

grant select on table "public"."customer_search_criteria" to "authenticated";

grant trigger on table "public"."customer_search_criteria" to "authenticated";

grant truncate on table "public"."customer_search_criteria" to "authenticated";

grant update on table "public"."customer_search_criteria" to "authenticated";

grant delete on table "public"."customer_search_criteria" to "service_role";

grant insert on table "public"."customer_search_criteria" to "service_role";

grant references on table "public"."customer_search_criteria" to "service_role";

grant select on table "public"."customer_search_criteria" to "service_role";

grant trigger on table "public"."customer_search_criteria" to "service_role";

grant truncate on table "public"."customer_search_criteria" to "service_role";

grant update on table "public"."customer_search_criteria" to "service_role";


