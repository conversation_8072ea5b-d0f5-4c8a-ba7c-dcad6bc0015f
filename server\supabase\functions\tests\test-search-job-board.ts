import { assertEquals, assertExists } from 'jsr:@std/assert';
import { MockTheirStack, TheirStackJobBoard, TheirStack } from '../search-job-board/job-search-theirstack.ts';
import { CustomerSearchCriteria, Job, SearchJobBoardsFound, Events } from '../shared/common.ts';
import { JOB_BOARD } from '../shared/common.ts';
import { setEvents } from '../search-job-board/index.ts';

// Mock Events implementation for testing
class MockEvents implements Events {
  public publishedSearchJobBoardsFound: SearchJobBoardsFound[] = [];
  public publishedCustSearchCriteriaCreated: any[] = [];

  async publishCustSearchCriteriaCreated(payload: any[], topic: string): Promise<void> {
    console.log(`Mock: Publishing ${payload.length} customer search criteria to topic ${topic}`);
    this.publishedCustSearchCriteriaCreated.push(...payload);
  }

  async publishSearchJobBoardsFound(payload: SearchJobBoardsFound[], topic: string): Promise<void> {
    console.log(`Mock: Publishing ${payload.length} job board search results to topic ${topic}`);
    this.publishedSearchJobBoardsFound.push(...payload);
  }

  reset() {
    this.publishedSearchJobBoardsFound = [];
    this.publishedCustSearchCriteriaCreated = [];
  }
}

// Mock Supabase client for testing
class MockSupabaseClient {
  private jobs: Map<string, any> = new Map();
  private customerJobs: Map<string, any> = new Map();
  private jobsByUrl: Map<string, any> = new Map();

  from(table: string) {
    if (table === 'jobs') {
      return {
        select: (columns: string) => ({
          eq: (column: string, value: string) => ({
            single: () => {
              if (column === 'url') {
                const job = this.jobsByUrl.get(value);
                return job
                  ? { data: job, error: null }
                  : { data: null, error: { code: 'PGRST116' } };
              }
              return { data: null, error: { code: 'PGRST116' } };
            }
          })
        }),
        insert: (data: any) => ({
          select: () => ({
            single: () => {
              this.jobs.set(data.id, data);
              this.jobsByUrl.set(data.url, data);
              return { data: data, error: null };
            }
          })
        })
      };
    }

    if (table === 'customer_jobs') {
      return {
        select: (columns: string) => ({
          eq: (column: string, value: string) => {
            const chainable = {
              eq: (column2: string, value2: string) => ({
                single: () => {
                  const key = `${value}-${value2}`;
                  const customerJob = this.customerJobs.get(key);
                  return customerJob
                    ? { data: customerJob, error: null }
                    : { data: null, error: { code: 'PGRST116' } };
                }
              })
            };
            return chainable;
          }
        })
      };
    }

    return {};
  }

  // Helper methods for testing
  addJob(job: any) {
    this.jobs.set(job.id, job);
    this.jobsByUrl.set(job.url, job);
  }

  addCustomerJob(customerId: string, jobId: string) {
    const key = `${customerId}-${jobId}`;
    this.customerJobs.set(key, { id: 1, customer_id: customerId, job_id: jobId });
  }

  reset() {
    this.jobs.clear();
    this.customerJobs.clear();
    this.jobsByUrl.clear();
  }

  getJobs() {
    return Array.from(this.jobs.values());
  }

  getCustomerJobs() {
    return Array.from(this.customerJobs.values());
  }
}

// Test data
const mockCustomerSearchCriteria: CustomerSearchCriteria = {
  id: "test-criteria-123",
  search_date: "2025-01-15",
  job_board_key: JOB_BOARD.THEIR_STACK,
  customer_id: "test-customer-456",
  job_titles: ["Software Engineer", "Frontend Developer"],
  locations: ["Remote", "New York"],
  search_status: "NEW"
};

const mockJob: Job = {
  id: "theirstack-1234",
  url: "https://example.com/job/1234",
  employer: "MockCompany",
  title: "Software Engineer",
  location: "Remote",
  pay_amount: 120000,
  pay_frequency: "Monthly" as any,
  pay_currency: "USD",
  languages: "JavaScript, TypeScript",
  visa_required: false,
  description: "This is a mock job for testing.",
  job_type: "FULLTIME" as any,
  job_status: "OPEN" as any,
  account_required: false,
  customer_apply: false
};

// Global test setup
let mockEvents: MockEvents;
let mockSupabase: MockSupabaseClient;

function setupMocks() {
  mockEvents = new MockEvents();
  mockSupabase = new MockSupabaseClient();
  setEvents(mockEvents);
}

function resetMocks() {
  mockEvents?.reset();
  mockSupabase?.reset();
}

// Simplified mock functions for testing
// function mockSaveJobIfNotExists(job: Job, supabase: MockSupabaseClient): Promise<Job | null> {
//   const result = (supabase.from('jobs').select('id, url') as any).eq('url', job.url).single();
//   if (result.data) {
//     console.log(`Job with URL ${job.url} already exists`);
//     return Promise.resolve(null);
//   }

//   const insertResult = (supabase.from('jobs').insert(job) as any).select().single();
//   if (insertResult.data) {
//     console.log(`Successfully saved job ${job.id}`);
//     return Promise.resolve(job);
//   }

//   return Promise.resolve(null);
// }

// function mockCheckCustomerJobExists(customerId: string, jobId: string, supabase: MockSupabaseClient): Promise<boolean> {
//   const result = ((supabase.from('customer_jobs').select('id') as any).eq('customer_id', customerId) as any).eq('job_id', jobId).single();
//   return Promise.resolve(!!result.data);
// }

async function mockSaveJobIfNotExists(job: Job, supabase: MockSupabaseClient): Promise<Job | null> {
  const { data: existingJob, error: selectError } = await supabase
    .from('jobs')
    .select('id, url')
    .eq('url', job.url)
    .single();

  if (existingJob) {
    console.log(`Job with URL ${job.url} already exists`);
    return null;
  }

  const { data: insertedJob, error: insertError } = await supabase
    .from('jobs')
    .insert(job)
    .select()
    .single();

  if (insertedJob) {
    console.log(`Successfully saved job ${job.id}`);
    return insertedJob;
  }

  return null;
}

async function mockCheckCustomerJobExists(customerId: string, jobId: string, supabase: MockSupabaseClient): Promise<boolean> {
  const { data: existing, error } = await supabase
    .from('customer_jobs')
    .select('id')
    .eq('customer_id', customerId)
    .eq('job_id', jobId)
    .single();

  return !!existing;
}


// Unit Tests
Deno.test("MockTheirStack returns expected job data", async () => {
  const jobBoard = new TheirStackJobBoard(undefined);

  const jobs = await jobBoard.search(mockCustomerSearchCriteria);

  assertEquals(jobs.length, 1);
  assertEquals(jobs[0].title, "Software Engineer");
  assertEquals(jobs[0].url, "https://example.com/job/1234");
  assertEquals(jobs[0].employer, "MockCompany");
  assertEquals(jobs[0].id, "theirstack-1234");
});

Deno.test("saveJobIfNotExists - saves new job successfully", async () => {
  setupMocks();

  const result = mockSupabase && await mockSaveJobIfNotExists(mockJob, mockSupabase);

  assertExists(result);
  assertEquals(result!.id, mockJob.id);
  assertEquals(result!.url, mockJob.url);

  const savedJobs = mockSupabase.getJobs();
  assertEquals(savedJobs.length, 1);
  assertEquals(savedJobs[0].id, mockJob.id);
});

Deno.test("saveJobIfNotExists - returns null for duplicate URL", async () => {
  setupMocks();

  // Add job first
  mockSupabase.addJob(mockJob);

  const result = await mockSaveJobIfNotExists(mockJob, mockSupabase);

  assertEquals(result, null);

  // Should still only have one job
  const savedJobs = mockSupabase.getJobs();
  assertEquals(savedJobs.length, 1);
});

Deno.test("checkCustomerJobExists - returns false when no relationship exists", async () => {
  setupMocks();

  const exists = await mockCheckCustomerJobExists("customer-123", "job-456", mockSupabase);

  assertEquals(exists, false);
});

Deno.test("checkCustomerJobExists - returns true when relationship exists", async () => {
  setupMocks();

  // Add customer-job relationship
  mockSupabase.addCustomerJob("customer-123", "job-456");

  const exists = await mockCheckCustomerJobExists("customer-123", "job-456", mockSupabase);

  assertEquals(exists, true);
});

Deno.test("MockEvents - captures published events correctly", async () => {
  setupMocks();

  const events: SearchJobBoardsFound[] = [
    { customerId: "customer-1", jobId: "job-1" },
    { customerId: "customer-2", jobId: "job-2" }
  ];

  await mockEvents.publishSearchJobBoardsFound(events, "test-topic");

  assertEquals(mockEvents.publishedSearchJobBoardsFound.length, 2);
  assertEquals(mockEvents.publishedSearchJobBoardsFound[0].customerId, "customer-1");
  assertEquals(mockEvents.publishedSearchJobBoardsFound[1].jobId, "job-2");
});

// Integration Tests
Deno.test("Integration: Full job search and processing flow - new jobs", async () => {
  setupMocks();

  const jobBoard = new TheirStackJobBoard(undefined);

  // Step 1: Search for jobs
  const foundJobs = await jobBoard.search(mockCustomerSearchCriteria);
  assertEquals(foundJobs.length, 1);

  // Step 2: Simulate saving jobs (no existing jobs)
  const savedJobs: Job[] = [];

  for (const job of foundJobs) {
    const savedJob = await mockSaveJobIfNotExists(job, mockSupabase);
    if (savedJob) {
      savedJobs.push(savedJob);
    }
  }

  assertEquals(savedJobs.length, 1);
  assertEquals(mockSupabase.getJobs().length, 1);

  // Step 3: Check customer jobs (no existing relationships)
  const newJobEvents: SearchJobBoardsFound[] = [];

  for (const job of savedJobs) {
    const exists = await mockCheckCustomerJobExists(mockCustomerSearchCriteria.customer_id, job.id, mockSupabase);
    if (!exists) {
      newJobEvents.push({ customerId: mockCustomerSearchCriteria.customer_id, jobId: job.id });
    }
  }

  assertEquals(newJobEvents.length, 1);
  assertEquals(newJobEvents[0].customerId, mockCustomerSearchCriteria.customer_id);
  assertEquals(newJobEvents[0].jobId, "theirstack-1234");

  // Step 4: Publish events
  await mockEvents.publishSearchJobBoardsFound(newJobEvents, "test-topic");
  assertEquals(mockEvents.publishedSearchJobBoardsFound.length, 1);
});

Deno.test("Integration: Full job search and processing flow - duplicate jobs", async () => {
  setupMocks();

  // Setup: Pre-populate with existing job
  const existingJob = { ...mockJob };
  mockSupabase.addJob(existingJob);

  const jobBoard = new TheirStackJobBoard(undefined);

  // Step 1: Search for jobs (will return same job)
  const foundJobs = await jobBoard.search(mockCustomerSearchCriteria);
  assertEquals(foundJobs.length, 1);

  // Step 2: Try to save jobs (should skip duplicates)
  const savedJobs: Job[] = [];

  for (const job of foundJobs) {
    const savedJob = await mockSaveJobIfNotExists(job, mockSupabase);
    if (savedJob) {
      savedJobs.push(savedJob);
    }
  }

  // Should have no new saved jobs due to duplicate URL
  assertEquals(savedJobs.length, 0);
  assertEquals(mockSupabase.getJobs().length, 1); // Still only the original job

  // Step 3: No new events should be created
  const newJobEvents: SearchJobBoardsFound[] = [];
  // Since no jobs were saved, no events to create

  assertEquals(newJobEvents.length, 0);

  // Step 4: No events to publish
  await mockEvents.publishSearchJobBoardsFound(newJobEvents, "test-topic");
  assertEquals(mockEvents.publishedSearchJobBoardsFound.length, 0);
});

Deno.test("Integration: Full job search and processing flow - existing customer job relationship", async () => {
  setupMocks();

  const jobBoard = new TheirStackJobBoard(undefined);

  // Step 1: Search and save jobs
  const foundJobs = await jobBoard.search(mockCustomerSearchCriteria);

  const savedJobs: Job[] = [];
  for (const job of foundJobs) {
    const savedJob = await mockSaveJobIfNotExists(job, mockSupabase);
    if (savedJob) {
      savedJobs.push(savedJob);
    }
  }

  assertEquals(savedJobs.length, 1);

  // Step 2: Pre-populate customer job relationship
  mockSupabase.addCustomerJob(mockCustomerSearchCriteria.customer_id, savedJobs[0].id);

  // Step 3: Check customer jobs (should find existing relationship)
  const newJobEvents: SearchJobBoardsFound[] = [];

  for (const job of savedJobs) {
    const exists = await mockCheckCustomerJobExists(mockCustomerSearchCriteria.customer_id, job.id, mockSupabase);
    if (!exists) {
      newJobEvents.push({ customerId: mockCustomerSearchCriteria.customer_id, jobId: job.id });
    }
  }

  // Should have no new events due to existing relationship
  assertEquals(newJobEvents.length, 0);

  // Step 4: No events to publish
  await mockEvents.publishSearchJobBoardsFound(newJobEvents, "test-topic");
  assertEquals(mockEvents.publishedSearchJobBoardsFound.length, 0);
});

// Edge case tests
Deno.test("Edge case: Multiple jobs with same URL", async () => {
  setupMocks();

  const job1 = { ...mockJob, id: "job-1" };
  const job2 = { ...mockJob, id: "job-2" }; // Same URL as job1

  // First job should save successfully
  const result1 = await mockSaveJobIfNotExists(job1, mockSupabase);
  assertExists(result1);
  assertEquals(result1!.id, "job-1");

  // Second job with same URL should be rejected
  const result2 = await mockSaveJobIfNotExists(job2, mockSupabase);
  assertEquals(result2, null);

  // Should only have one job saved
  assertEquals(mockSupabase.getJobs().length, 1);
  assertEquals(mockSupabase.getJobs()[0].id, "job-1");
});

Deno.test("Edge case: Empty job search results", async () => {
  setupMocks();

  // No jobs to save or process
  const savedJobs: Job[] = [];
  assertEquals(savedJobs.length, 0);

  // No events should be published
  await mockEvents.publishSearchJobBoardsFound([], "test-topic");
  assertEquals(mockEvents.publishedSearchJobBoardsFound.length, 0);
});

Deno.test("Edge case: Multiple customers for same job", async () => {
  setupMocks();

  const customerId1 = "customer-1";
  const customerId2 = "customer-2";
  const jobId = "job-123";

  // Add relationship for customer 1
  mockSupabase.addCustomerJob(customerId1, jobId);

  // Check customer 1 (should exist)
  const exists1 = await mockCheckCustomerJobExists(customerId1, jobId, mockSupabase);
  assertEquals(exists1, true);

  // Check customer 2 (should not exist)
  const exists2 = await mockCheckCustomerJobExists(customerId2, jobId, mockSupabase);
  assertEquals(exists2, false);
});

// Performance test with multiple jobs
Deno.test("Performance: Processing multiple jobs", async () => {
  setupMocks();

  // Create multiple mock jobs
  const jobs: Job[] = [];
  for (let i = 1; i <= 5; i++) {
    jobs.push({
      ...mockJob,
      id: `job-${i}`,
      url: `https://example.com/job/${i}`,
      title: `Software Engineer ${i}`
    });
  }

  // Save all jobs
  const savedJobs: Job[] = [];
  for (const job of jobs) {
    const savedJob = await mockSaveJobIfNotExists(job, mockSupabase);
    if (savedJob) {
      savedJobs.push(savedJob);
    }
  }

  assertEquals(savedJobs.length, 5);
  assertEquals(mockSupabase.getJobs().length, 5);

  // Check customer jobs for all (none should exist)
  const newJobEvents: SearchJobBoardsFound[] = [];
  for (const job of savedJobs) {
    const exists = await mockCheckCustomerJobExists(mockCustomerSearchCriteria.customer_id, job.id, mockSupabase);
    if (!exists) {
      newJobEvents.push({ customerId: mockCustomerSearchCriteria.customer_id, jobId: job.id });
    }
  }

  assertEquals(newJobEvents.length, 5);

  // Publish all events
  await mockEvents.publishSearchJobBoardsFound(newJobEvents, "test-topic");
  assertEquals(mockEvents.publishedSearchJobBoardsFound.length, 5);
});