import { TOPICS, isValidQstashRequest, getCustomerSearchCriteria, supabaseClient, SearchJobBoardsFound } from '../shared/common.ts';
import { Events } from '../shared/common.ts';
import { CustomerSearchCriteria, Job, LiveQstash } from '../shared/common.ts';
import { TheirStackJobBoard, LiveTheirStack } from './job-search-theirstack.ts';
import { SupabaseClient } from 'jsr:@supabase/supabase-js@2';
import { Database } from '../lib/database.types.ts';

import "https://deno.land/std@0.224.0/dotenv/load.ts";

console.log("Search Job Board Event Handler booting");

export function startDeno() {
  // just used by tests to start Deno.serve()
}

const supabase = supabaseClient();

Deno.serve(async (req: Request) => {

  const body = await req.text();
  const theirStackJobBoard = new TheirStackJobBoard(new LiveTheirStack());

  console.log("QSTASH_CURRENT_SIGNING_KEY", Deno.env.get("QSTASH_CURRENT_SIGNING_KEY"), "QSTASH_NEXT_SIGNING_KEY", Deno.env.get("QSTASH_NEXT_SIGNING_KEY"));


  if (await isValidQstashRequest(req.headers, body)) {

    const { customerSearchCriteriaId } = JSON.parse(body);

    const searchCriteria = await getCustomerSearchCriteria(customerSearchCriteriaId, supabase);

    const foundJobs: Job[] = await theirStackJobBoard.search(searchCriteria);
    console.log("searching job board with criteria", "jobBoard", foundJobs);

    // Save jobs to database with URL as unique constraint to avoid duplicates
    const savedJobs: Job[] = [];
    for (const job of foundJobs) {
      try {
        const savedJob = await saveJobIfNotExists(job, supabase);
        if (savedJob) {
          savedJobs.push(savedJob);
        }
      } catch (error) {
        console.error(`Failed to save job ${job.id}:`, error);
        // Continue processing other jobs even if one fails
      }
    }

    console.log(`Saved ${savedJobs.length} new jobs out of ${foundJobs.length} found jobs`);

    // Check customer_jobs and create events for new job matches
    const newJobEvents: SearchJobBoardsFound[] = [];
    for (const job of savedJobs) {
      const customerJobExists = await checkCustomerJobExists(searchCriteria.customer_id, job.id, supabase);
      if (!customerJobExists) {
        newJobEvents.push({ customerId: searchCriteria.customer_id, jobId: job.id });
      }
    }

    console.log(`Found ${newJobEvents.length} new job matches for customer ${searchCriteria.customer_id}`);

    // Publish events for new job matches
    if (newJobEvents.length > 0) {
      const topic = Deno.env.get(TOPICS.MATCH_CUSTOMER_JOB)!;
      await getEvents().publishSearchJobBoardsFound(newJobEvents, topic);
      console.log("sent batch search criterias to qstash");
    } else {
      console.log("No new job matches to publish");
    }

    return new Response("searched job board")

  } else {
    console.warn("not a valid requestion from Qstash, ignoring");
    return new Response(":p");
  }


})

/**
 * Allows a JobBoard to be searched for Jobs.
 * Specific JobBoards will provide an implementation.
 */
export interface JobBoardSearch {
  search(searchCriteria: CustomerSearchCriteria): Promise<Job[]>;
}


/**
 * Saves a job to the database if it doesn't already exist (based on URL).
 * Returns the saved job if successful, null if it already exists.
 */
async function saveJobIfNotExists(job: Job, supabase: SupabaseClient<Database>): Promise<Job | null> {
  try {
    // Check if job with this URL already exists
    const { data: existingJob, error: checkError } = await supabase
      .from("jobs")
      .select("id, url")
      .eq("url", job.url)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows returned
      throw checkError;
    }

    if (existingJob) {
      console.log(`Job with URL ${job.url} already exists with id ${existingJob.id}`);
      return null; // Job already exists
    }

    // Convert Job interface to database insert format
    const jobInsert = {
      id: job.id,
      url: job.url,
      employer: job.employer,
      title: job.title,
      location: job.location,
      pay_amount: job.pay_amount,
      pay_frequency: job.pay_frequency,
      pay_currency: job.pay_currency,
      languages: job.languages,
      visa_required: job.visa_required ? 'YES' : 'NO',
      description: job.description,
      job_type: job.job_type,
      job_status: job.job_status,
      account_required: job.account_required,
      customer_apply: job.customer_apply,
      create_src: 'AGENT' as const
    };

    // Insert the job
    const { error: insertError } = await supabase
      .from("jobs")
      .insert(jobInsert);

    if (insertError) {
      throw insertError;
    }

    console.log(`Successfully saved job ${job.id} with URL ${job.url}`);
    return job;

  } catch (error) {
    console.error(`Error saving job ${job.id}:`, error);
    throw error;
  }
}

/**
 * Checks if a customer_job entry exists for the given customer and job.
 */
async function checkCustomerJobExists(customerId: string, jobId: string, supabase: SupabaseClient<Database>): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from("customer_jobs")
      .select("id")
      .eq("customer_id", customerId)
      .eq("job_id", jobId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      throw error;
    }

    return !!data; // Returns true if data exists, false otherwise
  } catch (error) {
    console.error(`Error checking customer_job existence for customer ${customerId} and job ${jobId}:`, error);
    throw error;
  }
}

// this is to allow the test to set a mock
let qstash: Events;
export function setEvents(client: Events) {
  qstash = client;
}
function getEvents() {
  if (!qstash) {
    qstash = new LiveQstash();
  }
  return qstash;
}