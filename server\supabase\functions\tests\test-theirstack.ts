import { load } from 'jsr:@std/dotenv';
import { LiveTheirStack, TheirStackJobBoard } from '../search-job-board/job-search-theirstack.ts'
import { CustomerSearchCriteria } from '../shared/common.ts';
import { JOB_BOARD } from '../shared/common.ts';

Deno.test("ensures the payment event can be parsed", async () => {
    await load({ export:true });

    const criteria: CustomerSearchCriteria = {
        id: "123456",
        search_date: "2025-6-12",
        job_board_key: JOB_BOARD.THEIR_STACK,
        customer_id: "123A-456B",
        job_titles: ["Software Architect"],
        locations: ["New York"],
        search_status: "NEW"
    }

    const jobBoard = new TheirStackJobBoard(new LiveTheirStack());

    const jobs = await jobBoard.search(criteria);

    console.log("found jobs:", jobs);

    // TODO some validation here

});